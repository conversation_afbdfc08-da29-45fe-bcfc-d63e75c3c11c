package com.facishare.crm.fmcg.mengniu.business;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.mengniu.api.ValidateCode;
import com.facishare.crm.fmcg.tpm.business.dto.PayerInfoDTO;
import com.facishare.crm.fmcg.tpm.business.dto.ReceiverInfoDTO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardDistributeMethodEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardMethodEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardMethodTypeEnum;
import com.facishare.crm.fmcg.tpm.reward.handler.BigDateHandler;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QRCodeUtil;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.mengniu.api.FormQrcode;
import com.facishare.crm.fmcg.mengniu.api.QueryQrcodeStatus;
import com.facishare.crm.fmcg.mengniu.api.UseRebate;
import com.facishare.crm.fmcg.mengniu.business.abstraction.IDistributionService;
import com.facishare.crm.fmcg.tpm.retry.setter.SendCouponsSetter;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.FormatUtil;
import com.facishare.crm.fmcg.tpm.utils.Pair;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.FundAccountProxy;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.fund.CancelEntry;
import com.fmcg.framework.http.contract.fund.EnterAccount;
import com.fmcg.framework.http.contract.paas.data.AutoReduce;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.*;
import org.springframework.stereotype.Service;
import redis.clients.jedis.params.SetParams;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2024/1/24 17:00
 */
//IgnoreI18nFile
@Slf4j
@Service
public class DistributionService implements IDistributionService {


    public static final String AGREEMENT_OBJ_DISPLAY_AGREEMENT_ID = "field_K6Y5h__c";

    public static final String COST_OBJ_AGREEMENT_AMOUNT = "field_FEadz__c";

    public static final String UNION_PAY_DETAIL = "unionpay_sharing_details__c";

    public static final String REBATE_PREFIX = "rebate_prefix:";

    public static final String UNIQUE_TASK_REDIS_KEY = "MN_SEND_COUPONS_TASK:%s:%s";

    public static final ThreadLocal<SimpleDateFormat> DATE_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM"));

    public static final ThreadLocal<SimpleDateFormat> DATE_FORMAT2 = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy年MM月"));


    @Resource
    private ServiceFacade serviceFacade;

    @Resource(name = "redisCmd")
    private MergeJedisCmd jedisCmd;

    @Resource
    private RedissonClient redissonCmd;

    @Resource
    private PaasDataProxy paasDataProxy;

    @Resource
    private SendCouponsSetter sendCouponsSetter;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private FundAccountProxy fundAccountProxy;

    private RRateLimiter rateLimiter;

    @Resource
    private BigDateHandler bigDateHandler;


    @PostConstruct
    private void init() {
        rateLimiter = redissonCmd.getRateLimiter("MN_SEND_COUPONS_RATE_LIMITER");
        rateLimiter.setRate(RateType.OVERALL, 20, 1, RateIntervalUnit.SECONDS);
        ConfigFactory.getConfig("fs-fmcg-tpm-config", iConfig -> {
            int rate = iConfig.getInt("coupons_send_rate_setting", 20);
            rateLimiter.setRate(RateType.OVERALL, rate, 1, RateIntervalUnit.SECONDS);
        });
    }


    @Override
    public String sendCouponsByObject(String tenantId, String objectId, JSONObject extraData) {
        log.info("sendCouponsByObject tenantId:{}, objectId:{}", tenantId, objectId);
        User systemUser = User.systemUser(tenantId);
        IObjectData sendObject = serviceFacade.findObjectData(systemUser, objectId, "SendCouponsObj__c");
        Long month = LocalDateTime.ofInstant(Instant.ofEpochMilli(sendObject.get("month__c", Long.class)), ZoneId.systemDefault()).withDayOfMonth(1).withNano(0).withHour(0).withMinute(0).withSecond(0).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        List<String> feeTypes = sendObject.get("fee_type__c", List.class);
        String status = sendObject.get("has_done__c", String.class);
        tips("1".equals(status), "任务已经执行过了");
        tips(CollectionUtils.isEmpty(feeTypes), "费用类型不能为空");
        List<String> fixedDownstreamTenantIds = extraData.getJSONArray("fixed_tenant_accounts").toJavaList(String.class);
        String version = extraData.getString("version");
        createCostMonthField(tenantId);

        String uniqueKey = String.format(UNIQUE_TASK_REDIS_KEY, month, FormatUtil.join(feeTypes, "|"));
        String value = UUID.randomUUID().toString();

        try {
            tips(!"OK".equalsIgnoreCase(jedisCmd.set(uniqueKey, value, SetParams.setParams().nx().ex(60L))), "存在相同类型的任务正在执行中。");
            Map<String, IObjectData> tenantId2DealerMap = getDealerMap(tenantId, fixedDownstreamTenantIds);
            log.info("tenantIds:{}", tenantId2DealerMap.keySet());
            tenantId2DealerMap.forEach((downstreamTenantId, dealer) -> {
                if (Strings.isNullOrEmpty(version)) {
                    dealSingleTenantSendCoupons(tenantId, downstreamTenantId, objectId, dealer, month, feeTypes, extraData);
                } else if ("agreement".equals(version)) {
                    dealSingleTenantSendCouponsByAgreement(tenantId, downstreamTenantId, objectId, dealer, month, feeTypes, extraData);
                }
            });
            sendObject.set("has_done__c", "1");
            serviceFacade.updateObjectData(systemUser, sendObject);
        } finally {
            unlock(uniqueKey, value);
        }
        return "success";
    }

    private void unlock(String key, String value) {
        jedisCmd.eval("if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end", Lists.newArrayList(key), Lists.newArrayList(value));
    }

    @Override
    public String setSendCouponsTask(String tenantId, String objectId, JSONObject extraData) {
        log.info("sendCouponsByObject tenantId:{}, objectId:{}", tenantId, objectId);
        User systemUser = User.systemUser(tenantId);
        IObjectData sendObject = serviceFacade.findObjectData(systemUser, objectId, "SendCouponsObj__c");
        Long month = LocalDateTime.ofInstant(Instant.ofEpochMilli(sendObject.get("month__c", Long.class)), ZoneId.systemDefault()).withDayOfMonth(1).withNano(0).withHour(0).withMinute(0).withSecond(0).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        List<String> feeTypes = sendObject.get("fee_type__c", List.class);
        String status = sendObject.get("has_done__c", String.class);
        tips("1".equals(status), "任务已经执行过了");
        tips(CollectionUtils.isEmpty(feeTypes), "费用类型不能为空");
        List<String> fixedDownstreamTenantIds = extraData.getJSONArray("fixed_tenant_accounts").toJavaList(String.class);
        createCostMonthField(tenantId);

        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();

        task.submit(() -> {
            String uniqueKey = String.format(UNIQUE_TASK_REDIS_KEY, month, FormatUtil.join(feeTypes, "|"));
            String value = UUID.randomUUID().toString();
            try {
                tips(!"OK".equalsIgnoreCase(jedisCmd.set(uniqueKey, value, SetParams.setParams().nx().ex(300L))), "存在相同类型的任务正在执行中。");
                sameTaskCheck(tenantId, month, feeTypes);
                Map<String, IObjectData> tenantId2DealerMap = getDealerMap(tenantId, fixedDownstreamTenantIds);
                log.info("tenantIds:{}", tenantId2DealerMap.keySet());
                tenantId2DealerMap.forEach((downstreamTenantId, dealer) -> {
                    getOrCreateSendCouponsDetail(tenantId, downstreamTenantId, objectId, dealer);
                    sendCouponsSetter.setUpdateStatusTask(tenantId, downstreamTenantId, objectId, dealer.getId(), month, feeTypes, extraData, System.currentTimeMillis());
                });
            } catch (Exception e) {
                log.info("create send task err,", e);
            } finally {
                unlock(uniqueKey, value);
            }
        });
        task.run();

        return "success";
    }

    private void sameTaskCheck(String tenantId, long month, List<String> feeTypes) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter("month__c", Operator.EQL, Lists.newArrayList(String.valueOf(month))),
                SearchQueryUtil.filter("fee_type__c", Operator.EQ, feeTypes),
                SearchQueryUtil.filter("has_done__c", Operator.NEQ, Lists.newArrayList("1")),
                SearchQueryUtil.filter("has_done__c", Operator.IS, Lists.newArrayList())
        ), " 1 and 2 and (3 or 4)");
        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, "SendCouponsObj__c", query);
        tips(CollectionUtils.isEmpty(data), "存在相同类型的任务正在执行中。");
    }


    public void dealSingleTenantSendCoupons(String tenantId, String downstreamTenantId, String objectId, IObjectData dealer, long month, List<String> feeTypes, JSONObject extraData) {
        log.info("downstreamTenantId:{}, storeId:{},extraData:{},feeTypes:{}", downstreamTenantId, dealer.getId(), extraData, feeTypes);
        RLock lock = redissonCmd.getLock(String.format("dealSingleTenantSendCoupons:%s:%s", tenantId, month));
        try {
            lock.lock();
            IObjectData taskDetail = getSendCouponsDetail(tenantId, downstreamTenantId, objectId, dealer.getId());
            StringBuilder enterAccount = new StringBuilder();
            boolean isNeedEnterAccount = extraData.getBooleanValue("is_need_enter_account");
            boolean needCountTotal = extraData.getBooleanValue("need_count_total");
            boolean disableSendCoupons = extraData.getBooleanValue("disable_send_coupons");
            int baseSuccess = 0;
            int baseFail = 0;
            if (taskDetail != null) {
                String status = taskDetail.get("status__c", String.class);
                if ("2".equals(status) || "3".equals(status)) {
                    log.info("taskDetail has already done:{}", taskDetail);
                    return;
                }
                baseSuccess = taskDetail.get("success_count__c", Integer.class, 0);
                baseFail = taskDetail.get("fail_count__c", Integer.class, 0);
            }

            Set<String> storeTypes = new HashSet<>();
            if (CollectionUtils.isNotEmpty(extraData.getJSONArray("store_types"))) {
                storeTypes.addAll(extraData.getJSONArray("store_types").toJavaList(String.class));
            }
            AtomicInteger successCount = new AtomicInteger(baseSuccess);
            AtomicInteger failCount = new AtomicInteger(baseFail);
            User downstreamUser = User.systemUser(downstreamTenantId);
            List<String> crmUserIds = serviceFacade.getUsersByRole(downstreamUser, "00000000000000000000000000000006");
            if (CollectionUtils.isEmpty(crmUserIds)) {
                failCount.getAndIncrement();
                throw new ValidateException(I18N.text(I18NKeys.DISTRIBUTION_SERVICE_0));
            }

            createDealFlagField(downstreamTenantId);
            createStoreWriteOffField(downstreamTenantId);
            if (!disableSendCoupons) {
                List<String> forbidStoreIds = getForbidStoreIds(tenantId, dealer.getId());
                SearchTemplateQuery costQuery = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                                SearchQueryUtil.filter(TPMStoreWriteOffFields.SEND_STATUS, Operator.NEQ, Lists.newArrayList(TPMStoreWriteOffFields.SendStatus.SEND)),
                                SearchQueryUtil.filter(TPMStoreWriteOffFields.SEND_STATUS, Operator.IS, Lists.newArrayList()),
                                SearchQueryUtil.filter("field_u01Eo__c", Operator.EQ, Lists.newArrayList("1")),
                                SearchQueryUtil.filter(TPMStoreWriteOffFields.YEARS, Operator.EQ, Lists.newArrayList(String.valueOf(month)))),
                        "(1 or 2) and 3 and 4 ");
                log.info("forbidStoreIds:{}", forbidStoreIds);
                if (CollectionUtils.isNotEmpty(forbidStoreIds)) {
                    costQuery.getFilters().add(SearchQueryUtil.filter(TPMStoreWriteOffFields.STORE, Operator.NIN, forbidStoreIds));
                    costQuery.setPattern(costQuery.getPattern() + " and 5 ");
                }
                CommonUtils.executeInAllDataWithFields(serviceFacade, downstreamUser, ApiNames.TPM_STORE_WRITE_OFF_OBJ, costQuery, Lists.newArrayList(COST_OBJ_AGREEMENT_AMOUNT, TPMStoreWriteOffFields.CONFIRMED_AMOUNT, TPMStoreWriteOffFields.ACTIVITY_ASSOCIATION, TPMStoreWriteOffFields.STORE, TPMStoreWriteOffFields.YEARS, CommonFields.OBJECT_DESCRIBE_API_NAME, COST_OBJ_AGREEMENT_AMOUNT, TPMStoreWriteOffFields.ACTIVITY_ID, CommonFields.TENANT_ID), (dataList) -> {
                    Map<String, IObjectData> agreementMap = getAgreementMapByFeeType(downstreamTenantId, dataList.stream().map(data -> data.get(TPMStoreWriteOffFields.ACTIVITY_ASSOCIATION, String.class)).collect(Collectors.toList()), feeTypes, false);
                    List<IObjectData> stores = findByIdsAndReturnFields(downstreamTenantId, ApiNames.ACCOUNT_OBJ, dataList.stream().map(data -> data.get(TPMStoreWriteOffFields.STORE, String.class)).collect(Collectors.toList()), Lists.newArrayList(CommonFields.ID, CommonFields.NAME, CommonFields.OWNER, CommonFields.RECORD_TYPE, "store_type__c"));
                    //过滤门店
                    stores = stores.stream().filter(store -> {
                        String storeType = store.get("store_type__c", String.class);
                        return storeTypes.isEmpty() || storeTypes.contains(storeType) && CommonFields.RECORD_TYPE__DEFAULT.equals(store.getRecordType());
                    }).collect(Collectors.toList());
                    Map<String, IObjectData> storeMap = stores.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (a, b) -> a));
                    dataList.forEach(cost -> {
                        try {
                            String downstreamUserId = crmUserIds.get(0);
                            IObjectData agreement = agreementMap.get(cost.get(TPMStoreWriteOffFields.ACTIVITY_ASSOCIATION, String.class));
                            if (validateAgreement(agreement, month)) {
                                IObjectData store = storeMap.get(cost.get(TPMStoreWriteOffFields.STORE, String.class));
                                if (store == null) {
                                    log.info("没有找到门店信息或则门店已经被过滤,storeId:{}", cost.get(TPMStoreWriteOffFields.STORE, String.class));
                                    return;
                                }
                                String agreementCashingRecord = agreement.get("agreement_cashing_record__c", String.class);
                                if (!Strings.isNullOrEmpty(agreementCashingRecord) || hasRebate(downstreamTenantId, store, agreement)) {
                                    log.info("已经发送过优惠券了,cashing:{},cost:{}", agreementCashingRecord, cost.getName());
                                    cost.set(TPMStoreWriteOffFields.SEND_STATUS, TPMStoreWriteOffFields.SendStatus.SEND);
                                    cost.set(TPMStoreWriteOffFields.CONFIRMED_AMOUNT, cost.get(COST_OBJ_AGREEMENT_AMOUNT));
                                    return;
                                }
                                rateLimiter.acquire(1);
                                IObjectData rebate = formRebate(downstreamTenantId, downstreamUserId, month, cost, store, agreement);
                                tips(rebate == null, "生成返利单失败。");
                                successCount.incrementAndGet();
                                agreement.set("agreement_cashing_record__c", rebate.getId());
                                cost.set(TPMStoreWriteOffFields.SEND_STATUS, TPMStoreWriteOffFields.SendStatus.SEND);
                                cost.set(TPMStoreWriteOffFields.CONFIRMED_AMOUNT, cost.get(COST_OBJ_AGREEMENT_AMOUNT));
                                tryEnterAccount(downstreamTenantId, isNeedEnterAccount, rebate, enterAccount);
                            } else {
                                log.info("协议未找到，或则协议时不满足发券条件，cost:{},agreement:{}", cost.getName(), agreement);
                            }
                        } catch (Exception e) {
                            log.error("发送优惠券失败", e);
                            failCount.incrementAndGet();
                        }
                    });
                    try {
                        serviceFacade.batchUpdateByFields(downstreamUser, new ArrayList<>(agreementMap.values()), Lists.newArrayList("agreement_cashing_record__c"));
                        serviceFacade.batchUpdateByFields(downstreamUser, dataList, Lists.newArrayList(TPMStoreWriteOffFields.CONFIRMED_AMOUNT, TPMStoreWriteOffFields.SEND_STATUS));
                        updateTaskDetail(tenantId, downstreamTenantId, objectId, dealer, successCount, failCount, TraceContext.get().getTraceId(), "1", null);
                    } catch (Exception e) {
                        log.info("更新状态失败。", e);
                    }
                });
            }
            updateTaskDetail(tenantId, downstreamTenantId, objectId, dealer, successCount, failCount, TraceContext.get().getTraceId(), "2", null);
            if (needCountTotal) {
                countTotal(tenantId, downstreamTenantId, objectId, month, feeTypes, dealer, storeTypes);
            }
        } catch (Exception e) {
            log.error("{}执行发券失败.", downstreamTenantId, e);
            updateTaskDetail(tenantId, downstreamTenantId, objectId, dealer, null, null, TraceContext.get().getTraceId(), "3", e.getMessage());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void countTotal(String tenantId, String downstreamTenantId, String objectId, long month, List<String> feeTypes, IObjectData dealer, Collection<String> storeTypes) {
        List<String> forbidStoreIds = getForbidStoreIds(tenantId, dealer.getId());
        String monthStr1 = new SimpleDateFormat("yyyy年M月").format(new Date(month));
        SearchTemplateQuery searchCouponsQuery = SearchQueryUtil.formSimpleQuery(0, 1000, Lists.newArrayList(
                SearchQueryUtil.filter("field_2lk5Z__c", Operator.LIKE, Lists.newArrayList(monthStr1)),
                SearchQueryUtil.filter(CommonFields.IS_DELETED, Operator.EQ, Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue())))
        ));

        int couponsCount = serviceFacade.countObjectDataFromDB(downstreamTenantId, ApiNames.REBATE_OBJ, searchCouponsQuery);

        SearchTemplateQuery costQuery = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter("activity_association.name", Operator.LIKE, Lists.newArrayList(monthStr1)),
                SearchQueryUtil.filter("field_u01Eo__c", Operator.EQ, Lists.newArrayList("1")),
                SearchQueryUtil.filter(CommonFields.IS_DELETED, Operator.EQ, Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue())))
        ));
        if (CollectionUtils.isNotEmpty(forbidStoreIds)) {
            costQuery.getFilters().add(SearchQueryUtil.filter(TPMStoreWriteOffFields.STORE, Operator.NIN, forbidStoreIds));
        }
        if (CollectionUtils.isNotEmpty(storeTypes)) {
            costQuery.getFilters().add(SearchQueryUtil.filter(TPMStoreWriteOffFields.STORE + '.' + "store_type__c", Operator.IN, new ArrayList<>(storeTypes), true, null));
        }
        AtomicInteger costCount = new AtomicInteger(0);
        CommonUtils.executeInAllDataWithFields(serviceFacade, User.systemUser(downstreamTenantId), ApiNames.TPM_STORE_WRITE_OFF_OBJ, costQuery, Lists.newArrayList(CommonFields.ID, "activity_association"), partialData -> {
            List<String> agreementIds = partialData.stream().map(v -> v.get("activity_association", String.class)).collect(Collectors.toList());
            costCount.addAndGet(getAgreementMapByFeeType(downstreamTenantId, agreementIds, feeTypes, true));
        });
        log.info("{}:coupons:{},cost:{}", downstreamTenantId, couponsCount, costCount);
        updateTaskDetail(tenantId, downstreamTenantId, objectId, dealer, costCount, new AtomicInteger(couponsCount), null, null);
    }

    //todo:calculate redPacket
    private void countTotalByAgreement(String tenantId, String downstreamTenantId, String rewardType, String objectId, long month, List<String> auditStatus, List<String> feeTypes, IObjectData dealer, Collection<String> storeTypes, List<String> storeLevels) {
        List<String> forbidStoreIds = getForbidStoreIds(tenantId, dealer.getId());

        String monthStr1 = DATE_FORMAT.get().format(new Date(month));

        SearchTemplateQuery searchCouponsQuery = SearchQueryUtil.formSimpleQuery(0, 1000, Lists.newArrayList(
                SearchQueryUtil.filter("cost_month__c", Operator.EQ, Lists.newArrayList(monthStr1)),
                SearchQueryUtil.filter(CommonFields.IS_DELETED, Operator.EQ, Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue()))),
                SearchQueryUtil.filter(RedPacketRecordObjFields.TRIGGER_EVENT, Operator.EQ, Lists.newArrayList(RedPacketRecordObjFields.TriggerEvent.SEND_COUPONS)),
                SearchQueryUtil.filter(RedPacketRecordObjFields.RELATED_OBJECT_TENANT_ID, Operator.EQ, Lists.newArrayList(downstreamTenantId))
        ));

        int redPacketCount = serviceFacade.countObjectDataFromDB(tenantId, ApiNames.RED_PACKET_RECORD_OBJ, searchCouponsQuery);

        SearchTemplateQuery searchRedPacketQuery = SearchQueryUtil.formSimpleQuery(0, 1000, Lists.newArrayList(
                SearchQueryUtil.filter("cost_month__c", Operator.EQ, Lists.newArrayList(monthStr1)),
                SearchQueryUtil.filter(CommonFields.IS_DELETED, Operator.EQ, Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue())))
        ));

        int couponsCount = serviceFacade.countObjectDataFromDB(downstreamTenantId, ApiNames.REBATE_OBJ, searchRedPacketQuery);

        SearchTemplateQuery searchBankDetailQuery = SearchQueryUtil.formSimpleQuery(0, 1000, Lists.newArrayList(
                SearchQueryUtil.filter("cost_month__c", Operator.EQ, Lists.newArrayList(monthStr1)),
                SearchQueryUtil.filter(CommonFields.IS_DELETED, Operator.EQ, Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue())))
        ));

        int bankDetailCount = serviceFacade.countObjectDataFromDB(downstreamTenantId, UNION_PAY_DETAIL, searchBankDetailQuery);


        Pair<String, String> monthStartAndEndPoint = getMonthStartAndEndPoint(month);
        SearchTemplateQuery agreementQuery = SearchQueryUtil.formSimpleQuery(0, 1000, Lists.newArrayList(
                SearchQueryUtil.filter("field_Gvb1m__c", Operator.IN, auditStatus),
                SearchQueryUtil.filter(TPMActivityAgreementFields.BEGIN_DATE, Operator.BETWEEN, Lists.newArrayList(monthStartAndEndPoint.getFirst(), monthStartAndEndPoint.getSecond())),
                SearchQueryUtil.filter(TPMActivityAgreementFields.END_DATE, Operator.BETWEEN, Lists.newArrayList(monthStartAndEndPoint.getFirst(), monthStartAndEndPoint.getSecond())),
                SearchQueryUtil.filter("field_K6Y5h__c.field_vEf7l__c", Operator.IN, feeTypes, true, null),
                SearchQueryUtil.filter(CommonFields.IS_DELETED, Operator.EQ, Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue()))),
                SearchQueryUtil.filter(TPMActivityAgreementFields.ACTIVITY_ID + "." + "field_2FU3f__c", Operator.EQ, Lists.newArrayList("2"), true, null),
                SearchQueryUtil.filter(TPMActivityAgreementFields.ACTIVITY_ID + "." + "field_CBoh9__c", Operator.EQ, Lists.newArrayList("2"), true, null),
                SearchQueryUtil.filter("is_submint__c", Operator.EQ, Lists.newArrayList("2")),
                SearchQueryUtil.filter("field_80Od3__c", Operator.EQ, Lists.newArrayList("true"))));
        log.info("forbidStoreIds:{}", forbidStoreIds);
        if (CollectionUtils.isNotEmpty(forbidStoreIds)) {
            agreementQuery.getFilters().add(SearchQueryUtil.filter(TPMActivityAgreementFields.STORE_ID, Operator.NIN, forbidStoreIds));
        }
        if (CollectionUtils.isNotEmpty(storeLevels)) {
            agreementQuery.getFilters().add(SearchQueryUtil.filter(TPMActivityAgreementFields.STORE_ID + "." + "cert_level__c", Operator.IN, storeLevels, true, null));
        }
        if (CollectionUtils.isNotEmpty(storeTypes)) {
            agreementQuery.getFilters().add(SearchQueryUtil.filter(TPMActivityAgreementFields.STORE_ID + '.' + "store_type__c", Operator.IN, new ArrayList<>(storeTypes), true, null));
        }
        int agreementCount = serviceFacade.countObjectDataFromDB(downstreamTenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, agreementQuery);
        log.info("{}:coupons:{},agreementCount:{},redPacketCount:{},bankDetailCount:{}", downstreamTenantId, couponsCount, agreementCount, redPacketCount, bankDetailCount);
        updateTaskDetail(tenantId, downstreamTenantId, objectId, dealer, new AtomicInteger(agreementCount), new AtomicInteger(couponsCount), new AtomicInteger(redPacketCount), new AtomicInteger(bankDetailCount));
    }

    private boolean validateAgreement(IObjectData agreement, long month) {
        if (agreement != null) {
            long start = agreement.get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
            LocalDateTime startLocal = LocalDateTime.ofInstant(Instant.ofEpochMilli(start), ZoneId.systemDefault()).withDayOfMonth(1);
            LocalDateTime monthLocal = LocalDateTime.ofInstant(Instant.ofEpochMilli(month), ZoneId.systemDefault()).withDayOfMonth(1);
            return monthLocal.getMonthValue() == startLocal.getMonthValue();
        }
        return false;
    }

    private void tryEnterAccount(String tenantId, boolean isNeedEnterAccount, IObjectData rebate, StringBuilder enterAccount) {
        if (isNeedEnterAccount) {
            if (enterAccount.length() == 0) {
                SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                        SearchQueryUtil.filter(FundAccountFields.ACCOUNT_NO, Operator.EQ, Lists.newArrayList("JEFL01"))
                ));
                QueryDataUtil.find(serviceFacade, tenantId, ApiNames.FUND_ACCOUNT_OBJ, query, Lists.newArrayList(CommonFields.ID)).forEach(fundAccount -> {
                    enterAccount.append(fundAccount.getId());
                });
            }
            if (enterAccount.length() == 0) {
                log.info("找不到对应的返利账户。");
                return;
            }
            enterAccount(tenantId, rebate, enterAccount.toString());
        } else {
            log.info("不进行返利入账.");
        }
    }

    private void tips(boolean isTrigger, String msg) {
        if (isTrigger) {
            throw new ValidateException(msg);
        }
    }

    @Override
    public FormQrcode.Result formQrcode(FormQrcode.Arg arg) {
        ApiContext apiContext = ApiContextManager.getContext();
        Map<String, Object> data = new HashMap<>();
        data.put("tenantId", apiContext.getTenantId());
        data.put("content", arg.getContent());
        FormQrcode.Result result = new FormQrcode.Result();
        result.setImageBase64(QRCodeUtil.qrCode(arg.getWidth(), arg.getHeight(), JSON.toJSONString(data)));
        return result;
    }

    @Override
    public QueryQrcodeStatus.Result queryQrcodeStatus(QueryQrcodeStatus.Arg arg) {
        ApiContext apiContext = ApiContextManager.getContext();
        IObjectData rebateObj = serviceFacade.findObjectData(User.systemUser(apiContext.getTenantId()), arg.getContent(), ApiNames.REBATE_OBJ);

        return innerQuery(apiContext, rebateObj);
    }

    @Override
    public ValidateCode.Result validateCode(ValidateCode.Arg arg) {
        ValidateCode.Result result = new ValidateCode.Result();
        if (Strings.isNullOrEmpty(arg.getContent())) {
            throw new ValidateException("参数为空。");
        }
        ApiContext apiContext = ApiContextManager.getContext();
        String tenantId = apiContext.getTenantId();
        String[] ids = arg.getContent().split(",");
        List<IObjectData> rebates = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(ids), ApiNames.REBATE_OBJ);
        validateRebate(tenantId, rebates);
        return result;
    }

    private void validateRebate(String tenantId, List<IObjectData> rebates) {
        String globalUseType = null;
        String globalAccount = null;
        for (IObjectData rebate : rebates) {
             String useType = rebate.get(RebateFields.USE_TYPE, String.class);
             if(globalUseType == null){
                 globalUseType = useType;
             }else if(!globalUseType.equals(useType)){
                 throw new ValidateException("所选返利单的产品返货的使用类型不一致，请确认后重试");
             }
             String account = rebate.get(RebateFields.FUND_ACCOUNT_ID, String.class);
             if(Strings.isNullOrEmpty(account)){
                 throw new ValidateException("所选返利单存在未入账的返利单，请确认后重试");
             }else if(globalAccount == null){
                 globalAccount = account;
             }else if(!globalAccount.equals(account)){
                 throw new ValidateException("所选择返利单入账账户不一致，请确认后重试");
             }
             Long start = rebate.get(RebateFields.START_DATE, Long.class);
             Long end = rebate.get(RebateFields.END_DATE, Long.class);
             long now = System.currentTimeMillis();
             if(start == null || end == null || now > end || now < start){
                 
             }
        }
    }

    //todo：改成查db
    private QueryQrcodeStatus.Result innerQuery(ApiContext apiContext, IObjectData rebateObj) {
        QueryQrcodeStatus.Result result = new QueryQrcodeStatus.Result();
        result.setIsUsed(false);
        if (rebateObj.get(RebateFields.USED_AMOUNT, BigDecimal.class, BigDecimal.ZERO).compareTo(rebateObj.get(RebateFields.SUM_AMOUNT, BigDecimal.class)) != 0) {
            IFieldDescribe usedField = serviceFacade.findCustomFieldDescribe(apiContext.getTenantId(), ApiNames.REBATE_OBJ, RebateFields.USED_AMOUNT).getField();
            Map<String, Object> calculateMap = serviceFacade.calculateCountField(User.systemUser(apiContext.getTenantId()), ApiNames.REBATE_OBJ, rebateObj.getId(), Lists.newArrayList(new CountFieldDescribe(FieldDescribeExt.of(usedField).toMap())));
            BigDecimal used = new BigDecimal(String.valueOf(getOrDefault(calculateMap, RebateFields.USED_AMOUNT, 0D)));
            if (used.compareTo(rebateObj.get(RebateFields.SUM_AMOUNT, BigDecimal.class, BigDecimal.ZERO)) == 0) {
                result.setIsUsed(true);
            }
            log.info("rebate:{},used:{}", rebateObj.get(RebateFields.SUM_AMOUNT, BigDecimal.class), used);
        } else {
            result.setIsUsed(true);
        }
        return result;
    }

    private <T> T getOrDefault(Map<String, Object> map, String key, T defaultValue) {
        if (Objects.isNull(map.get(key))) {
            return defaultValue;
        }
        return (T) map.get(key);
    }

    @Override
    public UseRebate.Result useRebate(UseRebate.Arg arg) {
        ApiContext apiContext = ApiContextManager.getContext();
        String tenantId = apiContext.getTenantId();
        log.info("scanner :{},arg:{}", apiContext, arg);
        if (!tenantId.equals(arg.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKeys.DISTRIBUTION_SERVICE_1));
        }
        IObjectData rebateObj = serviceFacade.findObjectData(User.systemUser(arg.getTenantId()), arg.getContent(), ApiNames.REBATE_OBJ);

        if (innerQuery(apiContext, rebateObj).getIsUsed()) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.DISTRIBUTION_SERVICE_2), rebateObj.get(RebateFields.TOPIC, String.class)));
        }
        createScannerField(tenantId);

        String costMonth = rebateObj.get("cost_month__c", String.class);
        if (Strings.isNullOrEmpty(costMonth)) {
            throw new ValidateException(I18N.text(I18NKeys.DISTRIBUTION_SERVICE_3));
        }
        AutoReduce.Arg autoReduceArg = new AutoReduce.Arg();
        autoReduceArg.setId(rebateObj.getId());
        AutoReduce.Result autoReduceResult = paasDataProxy.autoReduce(Integer.parseInt(tenantId), -10000, autoReduceArg);
        log.info("result:{}", autoReduceResult);
        if (autoReduceResult.getErrCode() != 0) {
            log.info("返利优惠券使用失败：{}", autoReduceResult);
            throw new ValidateException(autoReduceResult.getErrMessage());
        }
        if (autoReduceResult.getResult() == null || !"success".equals(autoReduceResult.getResult().getResultCode())) {
            throw new ValidateException(I18N.text(I18NKeys.DISTRIBUTION_SERVICE_4));
        }
        try {
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put("scanner__c", Lists.newArrayList(apiContext.getEmployeeId().toString()));
            serviceFacade.updateWithMap(User.systemUser(tenantId), rebateObj, updateMap);
        } catch (Exception e) {
            log.info("update field fail.", e);
        }
        return new UseRebate.Result(String.format("“%s”兑付完成", rebateObj.get(RebateFields.TOPIC, String.class)));
    }

    @Override
    public void deleteDuplicateCoupons(String tenantId, List<String> fixedTenantAccounts, String type, JSONObject extraData) {

        try {
            Map<String, IObjectData> dealerMap = getDealerMap(tenantId, fixedTenantAccounts);
            for (String downloadTenantAccount : dealerMap.keySet()) {
                try {
                    TraceContext.get().setTraceId("deleteDuplicateCoupons:" + UUID.randomUUID());
                    if (Strings.isNullOrEmpty(type) || "1".equals(type)) {
                        findAndDeleteByAgreementId(downloadTenantAccount);
                    } else if ("2".equals(type)) {
                        findAndDeleteByAgreementId2(downloadTenantAccount, (String) extraData.getOrDefault("month", "2024-03"), (String) extraData.getOrDefault("agreement_name", "2024年1月"));
                    } else if ("3".equals(type)) {
                        findAndDeleteByAgreementId3(downloadTenantAccount, extraData);
                    } else if ("4".equals(type)) {
                        cancelInvalidAccount(downloadTenantAccount, extraData);
                    }
                } catch (Exception e) {
                    log.info("delete single err.", e);
                } finally {
                    TraceContext.remove();
                }
            }
        } catch (Exception e) {
            log.info("deleteDuplicateCoupons", e);
        } finally {
            TraceContext.remove();
        }
    }

    private void cancelInvalidAccount(String downloadTenantAccount, JSONObject extraData) {
        String tenantId = String.valueOf(eieaConverter.enterpriseAccountToId(downloadTenantAccount));
        log.info("deal tenant:{}", tenantId);
        String month = (String) extraData.getOrDefault("month", "2024-04");
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1,
                Lists.newArrayList(SearchQueryUtil.filter("cost_month__c", Operator.EQ, Lists.newArrayList(month)),
                        SearchQueryUtil.filter(CommonFields.IS_DELETED, Operator.EQ, Lists.newArrayList(String.valueOf(DELETE_STATUS.INVALID.getValue())))));
        List<String> failList = Lists.newArrayList();
        CommonUtils.executeInAllDataWithFields(serviceFacade, User.systemUser(tenantId), ApiNames.REBATE_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.NAME, CommonFields.ENTER_INTO_ACCOUNT), data -> {
            Lists.partition(data, 100).forEach(parts -> {
                List<IObjectData> backs = serviceFacade.bulkRecover(parts, User.systemUser(tenantId));
                backs.forEach(part -> {
                    int flag = deleteAndOutOfAccount(tenantId, part);
                    if (flag != 0) {
                        failList.add(part.getName());
                    }
                });
            });
        });
        if (CollectionUtils.isNotEmpty(failList)) {
            log.info("tenantId:{} has count:{}, values:{}", tenantId, failList.size(), failList);
        } else {
            log.info("tenantId:{},clear", tenantId);
        }
    }

    private void findAndDeleteByAgreementId3(String downloadTenantAccount, JSONObject extraData) {
        String tenantId = String.valueOf(eieaConverter.enterpriseAccountToId(downloadTenantAccount));
        log.info("deal tenant:{}", tenantId);
        validateRebateExists(tenantId);
        createOperateField(tenantId);
        String month = (String) extraData.getOrDefault("month", "2024-04");
        List<String> feeTypes = extraData.containsKey("fee_types") ? extraData.getJSONArray("fee_types").toJavaList(String.class) : Lists.newArrayList("2");
        List<String> storeTypes = extraData.containsKey("store_types") ? extraData.getJSONArray("store_types").toJavaList(String.class) : Lists.newArrayList("64aa5a171c3fd100016ba867", "652f9a8752956700015b1130", "64a0e32a7dafd200018cbeb6", "64a0e28aca15e600011ff4d9", "649fe5d4ca15e60001d05271", "649fe5638ac47800011f9323", "649fe2eaca15e60001ca54d5");
        String type = extraData.getString("type");

        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1,
                Lists.newArrayList(SearchQueryUtil.filter("cost_month__c", Operator.EQ, Lists.newArrayList(month)),
                        SearchQueryUtil.filter(RebateFields.UNUSED_AMOUNT, Operator.GT, Lists.newArrayList("0"))));
        if ("store".equals(type)) {
            query.getFilters().add(SearchQueryUtil.filter(RebateFields.ACCOUNT_ID + '.' + "store_type__c", Operator.NIN, storeTypes, true, null));
        }
        query.setSearchSource("db");

        AtomicInteger count = new AtomicInteger(0);
        CommonUtils.executeInAllDataWithFields(serviceFacade, User.systemUser(tenantId), ApiNames.REBATE_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.NAME, "field_b8zKv__c", CommonFields.ENTER_INTO_ACCOUNT), data -> {
            List<String> agreementIds = data.stream().map(v -> v.get("field_b8zKv__c", String.class)).collect(Collectors.toList());
            Map<String, IObjectData> agreementMap = getAgreementMapByFeeType(tenantId, agreementIds, feeTypes, false);
            if (MapUtils.isNotEmpty(agreementMap)) {
                data.stream().filter(v -> agreementMap.containsKey(v.get("field_b8zKv__c", String.class))).forEach(v -> {
                    try {
                        String agreementId = v.get("field_b8zKv__c", String.class);
                        count.getAndAdd(deleteAndOutOfAccount(tenantId, v));
                        Map<String, Object> updateMap = new HashMap<>();
                        updateMap.put("field_aVM1b__c", null);
                        updateMap.put("agreement_cashing_record__c", null);
                        serviceFacade.updateWithMap(User.systemUser(tenantId), agreementMap.get(agreementId), updateMap);
                    } catch (Exception e) {
                        log.info("delete single err.", e);
                    }
                });
            }
        });


        log.info("tenant:{} ,fail count:{}", tenantId, count.get());
    }

    @Override
    public void recoverData(String tenantId, List<String> fixedTenantAccounts) {
        try {
            Map<String, IObjectData> dealerMap = getDealerMap(tenantId, fixedTenantAccounts);
            for (String downloadTenantAccount : dealerMap.keySet()) {
                try {
                    TraceContext.get().setTraceId("recoverData:" + UUID.randomUUID());
                    recoverDataByMonth(downloadTenantAccount);
                } catch (Exception e) {
                    log.info("recoverData single err.", e);
                } finally {
                    TraceContext.remove();
                }
            }
        } catch (Exception e) {
            log.info("recoverData", e);
        } finally {
            TraceContext.remove();
        }
    }

    @Override
    public void dealSingleTenantSendCouponsByAgreement(String tenantId, String downstreamTenantId, String objectId, IObjectData dealer, long month, List<String> feeTypes, JSONObject extraData) {
        log.info("downstreamTenantId:{}, storeId:{},extraData:{},feeTypes:{}", downstreamTenantId, dealer.getId(), extraData, feeTypes);
        RLock lock = redissonCmd.getLock(String.format("dealSingleTenantSendCoupons:%s:%s", tenantId, month));
        try {
            lock.lock();
            IObjectData taskDetail = getSendCouponsDetail(tenantId, downstreamTenantId, objectId, dealer.getId());
            StringBuilder enterAccount = new StringBuilder();
            boolean isNeedEnterAccount = extraData.getBooleanValue("is_need_enter_account");
            boolean needCountTotal = extraData.getBooleanValue("need_count_total");
            boolean disableSendCoupons = extraData.getBooleanValue("disable_send_coupons");
            boolean disableRelatedIdFilter = extraData.getBooleanValue("disable_related_id_filter");
            //redPacket or coupons or cash
            String rewardType = (String) extraData.getOrDefault("reward_type", "coupons");
            //masterCloud or dealerCloud or marketUnionPayAccount
            String payAccountType = extraData.getString("pay_account_type");
            List<String> storeLevels = extraData.get("cert_level") != null ? extraData.getJSONArray("cert_level").toJavaList(String.class) : Lists.newArrayList();
            int baseSuccess = 0;
            int baseFail = 0;
            if (taskDetail != null) {
                String status = taskDetail.get("status__c", String.class);
                if ("2".equals(status) || "3".equals(status)) {
                    log.info("taskDetail has already done:{}", taskDetail);
                    return;
                }
                baseSuccess = taskDetail.get("success_count__c", Integer.class, 0);
                baseFail = taskDetail.get("fail_count__c", Integer.class, 0);
            }
            validateRebateExists(downstreamTenantId);
            validateRewardTypeAndPayAccount(rewardType, payAccountType, dealer);
            createRedPacketIdField(downstreamTenantId);
            createCashIdField(downstreamTenantId);

            Set<String> storeTypes = new HashSet<>();
            if (CollectionUtils.isNotEmpty(extraData.getJSONArray("store_types"))) {
                storeTypes.addAll(extraData.getJSONArray("store_types").toJavaList(String.class));
            }
            AtomicInteger successCount = new AtomicInteger(baseSuccess);
            AtomicInteger failCount = new AtomicInteger(baseFail);
            User downstreamUser = User.systemUser(downstreamTenantId);
            List<String> crmUserIds = serviceFacade.getUsersByRole(downstreamUser, "00000000000000000000000000000006");
            if (CollectionUtils.isEmpty(crmUserIds)) {
                failCount.getAndIncrement();
                throw new ValidateException(I18N.text(I18NKeys.DISTRIBUTION_SERVICE_5));
            }
            List<String> auditStatus = getAuditStatus(extraData);

            if (!disableSendCoupons) {
                Pair<String, String> monthStartAndEndPoint = getMonthStartAndEndPoint(month);
                List<String> forbidStoreIds = getForbidStoreIds(tenantId, dealer.getId());
                SearchTemplateQuery agreementQuery = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                        SearchQueryUtil.filter("field_Gvb1m__c", Operator.IN, auditStatus),
                        SearchQueryUtil.filter(TPMActivityAgreementFields.BEGIN_DATE, Operator.BETWEEN, Lists.newArrayList(monthStartAndEndPoint.getFirst(), monthStartAndEndPoint.getSecond())),
                        SearchQueryUtil.filter(TPMActivityAgreementFields.END_DATE, Operator.BETWEEN, Lists.newArrayList(monthStartAndEndPoint.getFirst(), monthStartAndEndPoint.getSecond())),
                        SearchQueryUtil.filter("field_K6Y5h__c.field_vEf7l__c", Operator.IN, feeTypes, true, null),
                        SearchQueryUtil.filter(TPMActivityAgreementFields.ACTIVITY_ID + "." + "field_2FU3f__c", Operator.EQ, Lists.newArrayList("2"), true, null),
                        SearchQueryUtil.filter(TPMActivityAgreementFields.ACTIVITY_ID + "." + "field_CBoh9__c", Operator.EQ, Lists.newArrayList("2"), true, null),
                        SearchQueryUtil.filter("is_submint__c", Operator.EQ, Lists.newArrayList("2")),
                        SearchQueryUtil.filter("field_80Od3__c", Operator.EQ, Lists.newArrayList("true"))
                ));
                log.info("forbidStoreIds:{}", forbidStoreIds);
                if (CollectionUtils.isNotEmpty(storeLevels)) {
                    agreementQuery.getFilters().add(SearchQueryUtil.filter(TPMActivityAgreementFields.STORE_ID + "." + "cert_level__c", Operator.IN, storeLevels, true, null));
                }
                if (CollectionUtils.isNotEmpty(forbidStoreIds)) {
                    agreementQuery.getFilters().add(SearchQueryUtil.filter(TPMActivityAgreementFields.STORE_ID, Operator.NIN, forbidStoreIds));
                }
                if (CollectionUtils.isNotEmpty(storeTypes)) {
                    agreementQuery.getFilters().add(SearchQueryUtil.filter("store_id.store_type__c", Operator.IN, new ArrayList<String>(storeTypes), true, null));
                }
                agreementQuery.setSearchSource("db");
                List<String> updateList = getUpdateListByRewardType(rewardType);
                if (!disableRelatedIdFilter) {
                    agreementQuery.getFilters().add(SearchQueryUtil.filter("agreement_cashing_record__c", Operator.IS, Lists.newArrayList()));
                    agreementQuery.getFilters().add(SearchQueryUtil.filter("agreement_red_packet_id__c", Operator.IS, Lists.newArrayList()));
                    agreementQuery.getFilters().add(SearchQueryUtil.filter("agreement_distribute_obj_id__c", Operator.IS, Lists.newArrayList()));
                }
                Map<String, IObjectData> storeAccountCache = new HashMap<>();
                CommonUtils.executeInAllDataWithFields(serviceFacade, downstreamUser, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, agreementQuery, Lists.newArrayList(CommonFields.NAME, CommonFields.ID, "agreement_cashing_record__c", TPMActivityAgreementFields.ACTUAL_TOTAL_AMOUNT, TPMActivityAgreementFields.STORE_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, TPMActivityAgreementFields.ACTIVITY_ID, CommonFields.TENANT_ID, "field_aVM1b__c", "agreement_red_packet_id__c", "agreement_distribute_obj_id__c"), (dataList) -> {
                    List<IObjectData> stores = findByIdsAndReturnFields(downstreamTenantId, ApiNames.ACCOUNT_OBJ, dataList.stream().map(data -> data.get(TPMActivityAgreementFields.STORE_ID, String.class)).collect(Collectors.toList()), Lists.newArrayList(CommonFields.ID, CommonFields.NAME, CommonFields.OWNER, CommonFields.RECORD_TYPE, "store_type__c"));
                    //过滤门店
                    Map<String, IObjectData> storeMap = stores.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (a, b) -> a));

                    dataList.forEach(agreement -> {
                        try {
                            String downstreamUserId = crmUserIds.get(0);
                            IObjectData store = storeMap.get(agreement.get(TPMActivityAgreementFields.STORE_ID, String.class));
                            if (store == null) {
                                log.info("没有找到门店信息或则门店已经被过滤,storeId:{}", agreement.get(TPMActivityAgreementFields.STORE_ID, String.class));
                                return;
                            }
                            if (Strings.isNullOrEmpty(rewardType) || "coupons".equals(rewardType)) {
                                sendCoupons(downstreamTenantId, downstreamUserId, month, store, agreement, isNeedEnterAccount, enterAccount, successCount);
                            } else if ("redPacket".equals(rewardType)) {
                                sendRedPacket(tenantId, downstreamTenantId, month, store, agreement, successCount, payAccountType);
                            } else {
                                IObjectData storeUnionPayAccount = getStoreUnionPayAccount(downstreamTenantId, store.getId(), storeAccountCache);
                                sendUnionPayDetail(downstreamTenantId, downstreamUserId, month, dealer, store, agreement, successCount, storeUnionPayAccount);
                            }
                        } catch (Exception e) {
                            log.error("发送奖励失败", e);
                            failCount.incrementAndGet();
                        }
                    });
                    try {
                        serviceFacade.batchUpdateByFields(downstreamUser, dataList, updateList);
                        updateTaskDetail(tenantId, downstreamTenantId, objectId, dealer, successCount, failCount, TraceContext.get().getTraceId(), "1", null);
                    } catch (Exception e) {
                        log.info("更新状态失败。", e);
                    }
                });
            }
            updateTaskDetail(tenantId, downstreamTenantId, objectId, dealer, successCount, failCount, TraceContext.get().getTraceId(), "2", null);
            if (needCountTotal) {
                //todo:need new logic
                countTotalByAgreement(tenantId, downstreamTenantId, rewardType, objectId, month, auditStatus, feeTypes, dealer, storeTypes, storeLevels);
            }
        } catch (Exception e) {
            log.error("{}执行发券失败.", downstreamTenantId, e);
            updateTaskDetail(tenantId, downstreamTenantId, objectId, dealer, null, null, TraceContext.get().getTraceId(), "3", e.getMessage());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private IObjectData getStoreUnionPayAccount(String downstreamTenantId, String id, Map<String, IObjectData> storeAccountCache) {
        //如果缓存里不存在就去查询并填写，如果存在直接返回账户对象
        if (!storeAccountCache.containsKey(id)) {
            SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                    SearchQueryUtil.filter("store__c", Operator.EQ, Lists.newArrayList(id))
            ));
            List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(downstreamTenantId), "unionpay_account_applicati__c", query);
            if (CollectionUtils.isNotEmpty(data)) {
                storeAccountCache.put(id, data.get(0));
            }
        }
        return storeAccountCache.get(id);
    }

    private void sendUnionPayDetail(String downstreamTenantId, String downloadUserId, long month, IObjectData dealer, IObjectData store, IObjectData agreement, AtomicInteger successCount, IObjectData storeUnionPayAccount) {
        if (hasSentUnionDetail(downstreamTenantId, store, agreement)) {
            log.info("已经发送过分账单了,agreementId:{},agreement:{}", agreement.getId(), agreement.getName());
            return;
        }
        rateLimiter.acquire(1);
        IObjectData unionDetail = formUnionDetail(downstreamTenantId, downloadUserId, month, store, agreement, dealer, storeUnionPayAccount);
        tips(unionDetail == null, "生成分账单失败。");
        successCount.incrementAndGet();
        agreement.set("agreement_distribute_obj_id__c", unionDetail.getId());
        agreement.set("agreement_distribute_obj_name__c", unionDetail.getName());
        agreement.set("agreement_distribute_obj_api_name__c", UNION_PAY_DETAIL);
    }

    private IObjectData formUnionDetail(String downstreamTenantId, String downloadUserId, long month, IObjectData store, IObjectData agreement, IObjectData dealer, IObjectData storeUnionPayAccount) {
        IObjectData detail = formObjectData(downstreamTenantId, UNION_PAY_DETAIL, "default__c", Lists.newArrayList(downloadUserId));
        detail.set("cost_month__c", DATE_FORMAT.get().format(new Date(month)));
        detail.set("agreement_fee__c", agreement.get(TPMActivityAgreementFields.ACTUAL_TOTAL_AMOUNT));
        detail.set("dealer_name__c", dealer.getId());
        detail.set("agreement_id__c", agreement.getId());
        if (storeUnionPayAccount != null) {
            detail.set("store_owner_account__c", storeUnionPayAccount.get("acct_no__c"));
        }
        detail.set("store_id__c", store.getId());
        detail.set("store_name__c", store.getId());
        detail.set("agreement_name__c", agreement.getName());
        detail.set("distributor_account__c", dealer.get("unionpay_withdrawal_accoun__c"));
        detail.set("business_account__c", eieaConverter.enterpriseIdToAccount(Integer.parseInt(downstreamTenantId)));
        return serviceFacade.saveObjectData(User.systemUser(downstreamTenantId), detail);
    }

    private boolean hasSentUnionDetail(String tenantId, IObjectData store, IObjectData agreement) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter("store_id__c", Operator.EQ, Lists.newArrayList(store.getId())),
                SearchQueryUtil.filter("agreement_id__c", Operator.EQ, Lists.newArrayList(agreement.getId()))
        ));
        List<IObjectData> dataList = QueryDataUtil.find(serviceFacade, tenantId, UNION_PAY_DETAIL, query, Lists.newArrayList(CommonFields.ID, CommonFields.NAME, CommonFields.OBJECT_DESCRIBE_API_NAME));
        if (CollectionUtils.isEmpty(dataList)) {
            return false;
        } else {
            agreement.set("agreement_distribute_obj_id__c", dataList.get(0).getId());
            agreement.set("agreement_distribute_obj_name__c", dataList.get(0).getName());
            agreement.set("agreement_distribute_obj_api_name__c", dataList.get(0).getDescribeApiName());
            return true;
        }
    }

    private List<String> getUpdateListByRewardType(String rewardType) {
        List<String> updateList;
        if (Strings.isNullOrEmpty(rewardType) || "coupons".equals(rewardType)) {
            updateList = Lists.newArrayList("field_aVM1b__c", "agreement_cashing_record__c");
        } else if ("redPacket".equals(rewardType)) {
            updateList = Lists.newArrayList("field_aVM1b__c", "agreement_red_packet_id__c", "agreement_red_packet_name__c");
        } else {
            // 银联的无需更新field_aVM1b__c
            updateList = Lists.newArrayList("agreement_distribute_obj_id__c", "agreement_distribute_obj_name__c", "agreement_distribute_obj_api_name__c");
        }
        return updateList;
    }

    private void sendRedPacket(String tenantId, String downstreamTenantId, long month, IObjectData store, IObjectData agreement, AtomicInteger successCount, String payAccountType) {
        if (hasSentRedPacket(tenantId, store, agreement)) {
            log.info("已经发送过红包了,agreementId:{},agreement:{}", agreement.getId(), agreement.getName());
            if (Strings.isNullOrEmpty(agreement.get("field_aVM1b__c", String.class))) {
                agreement.set("field_aVM1b__c", "5");
            }
            return;
        }
        rateLimiter.acquire(1);
        IObjectData redPacket = formRedPacket(tenantId, downstreamTenantId, month, store, agreement, payAccountType);
        tips(redPacket == null, "生成红包失败。");
        successCount.incrementAndGet();
        agreement.set("agreement_red_packet_id__c", redPacket.getId());
        agreement.set("agreement_red_packet_name__c", redPacket.getName());
        agreement.set("field_aVM1b__c", "5");
    }

    private IObjectData formRedPacket(String upperTenantId, String downstreamTenantId, long month, IObjectData store, IObjectData agreement, String payAccountType) {
        ReceiverInfoDTO receiverInfoDTO = new ReceiverInfoDTO();
        receiverInfoDTO.setTenantId(downstreamTenantId);
        receiverInfoDTO.setTenantName(bigDateHandler.getTenantName(receiverInfoDTO.getTenantId()));
        receiverInfoDTO.setType(RedPacketRecordObjFields.TransfereeAccountType.WECHAT);
        receiverInfoDTO.setStore(store);
        BigDecimal amount = agreement.get(TPMActivityAgreementFields.ACTUAL_TOTAL_AMOUNT, BigDecimal.class);
        receiverInfoDTO.setAmount(amount);
        IObjectData user = bigDateHandler.getStoreMaster(downstreamTenantId, store.getId());
        bigDateHandler.fillReceiverRoleInfo(user, receiverInfoDTO);
        bigDateHandler.fillReceiverIdentityInfo(upperTenantId, user, receiverInfoDTO);
        receiverInfoDTO.setDistributionType(RewardDistributeMethodEnum.WITHDRAW.code());
        receiverInfoDTO.setRewardMethod(RewardMethodEnum.RED_PACKET.code());
        receiverInfoDTO.setRewardMethodType(RewardMethodTypeEnum.SOLID_RED_PACKET.code());

        PayerInfoDTO payerInfoDTO = new PayerInfoDTO();

        if (payAccountType.equals("dealerCloud")) {
            payerInfoDTO.setTenantId(downstreamTenantId);
        } else {
            payerInfoDTO.setTenantId(upperTenantId);
        }
        payerInfoDTO.setType(RedPacketRecordObjFields.TransferorAccountType.CLOUD);
        payerInfoDTO.setTenantName(bigDateHandler.getTenantName(payerInfoDTO.getTenantId()));
        payerInfoDTO.setAccount("default");
        String identity = String.format("chenlie_%s_%s", agreement.getDescribeApiName(), agreement.getId());
        Date monthDate = new Date(month);
        String remark = DATE_FORMAT2.get().format(monthDate) + "陈列红包";
        IObjectData redPacket = bigDateHandler.formRedPacket(upperTenantId, TraceContext.get().getTraceId(), payerInfoDTO, receiverInfoDTO, agreement, null, null, null, null, remark, identity);
        redPacket.set(RedPacketRecordObjFields.TRIGGER_EVENT, RedPacketRecordObjFields.TriggerEvent.SEND_COUPONS);
        redPacket.set("cost_month__c", DATE_FORMAT.get().format(monthDate));
        redPacket.set(RedPacketRecordObjFields.EVENT_TYPE, RedPacketRecordObjFields.EventType.DISPLAY_CASH);
        IObjectData detail = bigDateHandler.formRedPacketDetail(upperTenantId, null, amount, null, TraceContext.get().getTraceId());

        Map<String, List<IObjectData>> details = new HashMap<>();
        details.put(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ, Lists.newArrayList(detail));
        SaveMasterAndDetailData.Arg arg = SaveMasterAndDetailData.Arg.builder()
                .objectDescribes(loadDescribeMap(upperTenantId))
                .masterObjectData(redPacket)
                .detailObjectData(details)
                .build();

        SaveMasterAndDetailData.Result result = serviceFacade.saveMasterAndDetailData(User.systemUser(upperTenantId), arg);
        redPacket = result.getMasterObjectData();

        return redPacket;
    }

    private Map<String, IObjectDescribe> loadDescribeMap(String tenantId) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(ApiNames.RED_PACKET_RECORD_OBJ, serviceFacade.findObject(tenantId, ApiNames.RED_PACKET_RECORD_OBJ));
        describeMap.put(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ, serviceFacade.findObject(tenantId, ApiNames.RED_PACKET_RECORD_DETAIL_OBJ));
        return describeMap;
    }

    private void sendCoupons(String downstreamTenantId, String downstreamUserId, long month, IObjectData store, IObjectData agreement, boolean isNeedEnterAccount, StringBuilder enterAccount, AtomicInteger successCount) {
        String agreementCashingRecord = agreement.get("agreement_cashing_record__c", String.class);
        if (hasRebate(downstreamTenantId, store, agreement)) {
            log.info("已经发送过优惠券了,cashing:{},agreement:{}", agreementCashingRecord, agreement.getName());
            if (Strings.isNullOrEmpty(agreement.get("field_aVM1b__c", String.class))) {
                agreement.set("field_aVM1b__c", "1");
            }
            return;
        }
        rateLimiter.acquire(1);
        IObjectData rebate = formRebate(downstreamTenantId, downstreamUserId, month, null, store, agreement);
        tips(rebate == null, "生成返利单失败。");
        successCount.incrementAndGet();
        agreement.set("agreement_cashing_record__c", rebate.getId());
        agreement.set("field_aVM1b__c", "1");
        tryEnterAccount(downstreamTenantId, isNeedEnterAccount, rebate, enterAccount);
    }

    private Pair<String, String> getMonthStartAndEndPoint(long month) {
        LocalDateTime monthBegin = LocalDateTime.ofInstant(Instant.ofEpochMilli(month), ZoneId.systemDefault()).withDayOfMonth(1).withNano(0).withSecond(0).withHour(0).withMinute(0);
        LocalDateTime monthEnd = monthBegin.plusMonths(1).withDayOfMonth(1).withNano(0).withSecond(0).withHour(0).withMinute(0);
        return new Pair<>(String.valueOf(monthBegin.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()), String.valueOf(monthEnd.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()));
    }

    private List<String> getAuditStatus(JSONObject extraData) {
        List<String> auditStatus = Lists.newArrayList("9", "11", "13");
        if (extraData.containsKey("audit_status")) {
            auditStatus = extraData.getJSONArray("audit_status").toJavaList(String.class);
        }
        log.info("audit status:{}", auditStatus);
        return auditStatus;
    }


    private void recoverDataByMonth(String downloadTenantAccount) {
        String tenantId = String.valueOf(eieaConverter.enterpriseAccountToId(downloadTenantAccount));
        log.info("deal tenant:{}", tenantId);
        validateRebateExists(tenantId);
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1,
                Lists.newArrayList(SearchQueryUtil.filter("cost_month__c", Operator.EQ, Lists.newArrayList("2024-04")),
                        SearchQueryUtil.filter("cost_month__c", Operator.EQ, Lists.newArrayList("2024-04")),
                        SearchQueryUtil.filter(CommonFields.IS_DELETED, Operator.EQ, Lists.newArrayList("true"))));
        query.setPattern("(1 or 2) and 3");
        List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.REBATE_OBJ, query);
        log.info("data size:{}", dataList.size());
        Lists.partition(dataList, 200).forEach(parts -> {
            serviceFacade.bulkRecover(parts, User.systemUser(tenantId));
            parts.forEach(data -> tryEnterAccount(tenantId, true, data, new StringBuilder()));
        });

    }

    private void findAndDeleteByAgreementId(String downloadTenantAccount) {
        String tenantId = String.valueOf(eieaConverter.enterpriseAccountToId(downloadTenantAccount));
        log.info("deal tenant:{}", tenantId);
        validateRebateExists(tenantId);
        createOperateField(tenantId);

        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 2000,
                Lists.newArrayList(SearchQueryUtil.filter("field_b8zKv__c", Operator.ISN, Lists.newArrayList())));
        query.setSearchSource("db");

        List<IObjectData> allData = serviceFacade.aggregateFindBySearchQuery(tenantId, query, ApiNames.REBATE_OBJ, "field_b8zKv__c", "count", "");
        log.info("total count:{}", allData.size());
        allData = allData.stream().filter(data -> {
            Integer count = data.get("groupbycount", Integer.class);
            return Objects.nonNull(count) && count > 1;
        }).collect(Collectors.toList());
        log.info("tenant：{} need count:{}", tenantId, allData.size());
        AtomicInteger count = new AtomicInteger(0);
        Lists.partition(allData, 200).stream().forEach(partList -> {
            List<String> agreementIds = partList.stream().map(data -> data.get("field_b8zKv__c", String.class)).collect(Collectors.toList());
            SearchTemplateQuery agreementCouponsQuery = SearchQueryUtil.formSimpleQuery(0, -1,
                    Lists.newArrayList(SearchQueryUtil.filter("field_b8zKv__c", Operator.IN, agreementIds)));
            List<IObjectData> coupons = CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.REBATE_OBJ, agreementCouponsQuery, Lists.newArrayList(CommonFields.ID, "field_b8zKv__c", CommonFields.CREATE_TIME, CommonFields.ENTER_INTO_ACCOUNT, CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.NAME));
            coupons.stream().collect(Collectors.groupingBy(data -> data.get("field_b8zKv__c", String.class))).forEach((agreement, sameAgreementCoupons) -> {
                if (sameAgreementCoupons.size() > 1) {
                    sameAgreementCoupons.sort((a, b) -> a.get(CommonFields.CREATE_TIME, Long.class, 0L) < b.get(CommonFields.CREATE_TIME, Long.class, 0L) ? 1 : -1);
                    for (int i = 0; i < sameAgreementCoupons.size() - 1; i++) {
                        count.getAndAdd(deleteAndOutOfAccount(tenantId, sameAgreementCoupons.get(i)));
                    }
                }
            });
        });
        log.info("tenant:{} ,fail count:{}", tenantId, count.get());
    }

    private void findAndDeleteByAgreementId2(String downloadTenantAccount, String month, String agreementContains) {
        String tenantId = String.valueOf(eieaConverter.enterpriseAccountToId(downloadTenantAccount));
        log.info("deal tenant:{}", tenantId);
        validateRebateExists(tenantId);
        createOperateField(tenantId);
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter("cost_month__c", Operator.EQ, Lists.newArrayList(month)),
                SearchQueryUtil.filter("field_2lk5Z__c", Operator.CONTAINS, Lists.newArrayList(agreementContains))
        ));
        log.info("month:{},agreementContains:{}", month, agreementContains);
        log.info("query:{}", query);
        AtomicInteger count = new AtomicInteger(0);
        CommonUtils.executeInAllDataWithFields(serviceFacade, User.systemUser(tenantId), ApiNames.REBATE_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.NAME, CommonFields.ENTER_INTO_ACCOUNT), data -> {
            log.info("size:{}", data.size());
            data.forEach(v -> count.getAndAdd(deleteAndOutOfAccount(tenantId, v)));
        });
        log.info("finish.tenantId:{},fail:{}", tenantId, count);
    }

    private void validateRebateExists(String tenantId) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.REBATE_OBJ);
        if (describe.getFieldDescribe("field_b8zKv__c") == null) {
            log.info("返利券协议字段不存在");
            throw new ValidateException(I18N.text(I18NKeys.DISTRIBUTION_SERVICE_6));
        }
    }

    private int deleteAndOutOfAccount(String tenantId, IObjectData rebate) {
        boolean enterAccount = rebate.get(CommonFields.ENTER_INTO_ACCOUNT, Boolean.class, Boolean.FALSE);
        if (enterAccount) {
            try {
                cancelAccount(tenantId, ApiNames.REBATE_OBJ, rebate.getId());
            } catch (Exception e) {
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put("operate_message__c", "取消入账失败");
                log.info("取消入账失败：{}", rebate);
                serviceFacade.updateWithMap(User.systemUser(tenantId), rebate, updateMap);
                return 1;
            }
        }
        serviceFacade.invalid(rebate, User.systemUser(tenantId));
        return 0;
    }

    private void updateTaskDetail(String tenantId, String downstreamTenantId, String objectId, IObjectData store, AtomicInteger needCount, AtomicInteger couponsCount, AtomicInteger redPacketCount, AtomicInteger unionPayDetailCount) {
        IObjectData detail = getOrCreateSendCouponsDetail(tenantId, downstreamTenantId, objectId, store);
        if (needCount != null) {
            detail.set("total_count__c", needCount.get());
        }
        if (couponsCount != null) {
            detail.set("coupons_count__c", couponsCount.get());
        }
        if (redPacketCount != null) {
            detail.set("red_packet_count__c", redPacketCount.get());
        }
        if (unionPayDetailCount != null) {
            detail.set("union_pay_detail_count__c", unionPayDetailCount.get());
        }
        serviceFacade.updateObjectData(User.systemUser(tenantId), detail);
    }

    private void updateTaskDetail(String tenantId, String downstreamTenantId, String objectId, IObjectData store, AtomicInteger successCount, AtomicInteger failCount, String traceId, String status, String errorMsg) {

        IObjectData detail = getOrCreateSendCouponsDetail(tenantId, downstreamTenantId, objectId, store);
        if (successCount != null) {
            detail.set("success_count__c", successCount.get());
        }

        if (failCount != null) {
            detail.set("fail_count__c", failCount.get());
        }

        if (!Strings.isNullOrEmpty(status)) {
            detail.set("status__c", status);
        }
        if (!Strings.isNullOrEmpty(traceId)) {
            detail.set("trace_id__c", traceId);
        }

        if (!Strings.isNullOrEmpty(errorMsg)) {
            detail.set("error_message__c", errorMsg);
        }
        serviceFacade.updateObjectData(User.systemUser(tenantId), detail);
    }

    private IObjectData getOrCreateSendCouponsDetail(String tenantId, String downstreamTenantId, String objectId, IObjectData store) {
        IObjectData detail = getSendCouponsDetail(tenantId, downstreamTenantId, objectId, store.getId());
        if (detail == null) {
            detail = formObjectData(tenantId, "SendCouponsDetailObj__c", "default__c", Lists.newArrayList("-10000"));
            detail.set("status__c", "0");
            detail.set("account_id__c", store.getId());
            detail.set("master__c", objectId);
            detail.set("tenant_id__c", downstreamTenantId);
            detail = serviceFacade.saveObjectData(User.systemUser(tenantId), detail);
        }
        return detail;
    }

    private IObjectData getSendCouponsDetail(String tenantId, String downstreamTenantId, String objectId, String storeId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter("master__c", Operator.EQ, Lists.newArrayList(objectId)),
                SearchQueryUtil.filter("tenant_id__c", Operator.EQ, Lists.newArrayList(downstreamTenantId)),
                SearchQueryUtil.filter("account_id__c", Operator.EQ, Lists.newArrayList(storeId))
        ));
        List<IObjectData> details = QueryDataUtil.find(serviceFacade, tenantId, "SendCouponsDetailObj__c", query);
        return CollectionUtils.isEmpty(details) ? null : details.get(0);
    }

    private IObjectData formObjectData(String tenantId, String apiName, String recordType, List<String> owner) {
        IObjectData objectData = new ObjectData();
        objectData.setOwner(owner);
        objectData.setRecordType(recordType);
        objectData.setDescribeApiName(apiName);
        objectData.setTenantId(tenantId);
        return objectData;
    }

    private IObjectData formRebate(String tenantId, String userId, long month, IObjectData cost, IObjectData store, IObjectData agreement) {
        if (store == null) {
            log.info("生成返利单时门店为空，cost:{},agreement", cost, agreement);
            return null;
        }
        long now = System.currentTimeMillis();
        long endDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(month), ZoneId.systemDefault()).plusMonths(2).withDayOfMonth(1).withNano(0).withHour(0).withMinute(0).withSecond(0).toInstant(ZoneOffset.of("+8")).toEpochMilli() - 1;
        IObjectData rebate = formObjectData(tenantId, ApiNames.REBATE_OBJ, "default__c", Lists.newArrayList(userId));
        rebate.set(RebateFields.TOPIC, DATE_FORMAT2.get().format(new Date(month)) + "陈列优惠券");
        rebate.set(RebateFields.ACCOUNT_ID, store.getId());
        rebate.set(RebateFields.ACTIVE_STATUS, "enable");
        rebate.set(RebateFields.REBATE_TYPE, RebateFields.REBATE_TYPE__MONEY);
        rebate.set(RebateFields.USE_TYPE, "Cash");
        rebate.set(RebateFields.SUM_AMOUNT, agreement.get(TPMActivityAgreementFields.ACTUAL_TOTAL_AMOUNT));
        rebate.set("cost_month__c", DATE_FORMAT.get().format(new Date(month)));
        rebate.set("field_b8zKv__c", agreement.getId());
        rebate.set("field_2lk5Z__c", agreement.getName());
        rebate.set("field_3H8Bp__c", agreement.get(TPMActivityAgreementFields.ACTIVITY_ID));
        if (cost != null) {
            rebate.set("store_write_off__c", cost.getId());
        }
        rebate.set("start_date", now < endDate ? now : month);
        rebate.set("end_date", endDate);
        rebate.set("remark", "陈列优惠券");
        rebate.set(RebateFields.ADD_SOURCE, RebateFields.AddSource.DISPLAY);
        /*rebate.set(RebateFields.PRODUCT_CONDITION_CONTENT, "{\"data\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"product_id.origin_source\",\"field_name__s\":\"产品名称.数据同步\",\"field_values\":[\"0\"],\"field_values__s\":\"数据同步\",\"object_api_name\":\"SalesOrderProductObj\",\"object_api_name__s\":\"订单产品\",\"operator\":\"EQ\",\"operator__s\":\"等于\",\"operator_name\":\"等于\",\"type\":\"select_one\"}]}],\"gift_condition_unit_id\":\"\",\"object_api_name\":\"SalesOrderProductObj\"}");
        rebate.set(RebateFields.PRODUCT_RANGE_TYPE, "ALL");
        rebate.set(RebateFields.PRODUCT_CONDITION_TYPE, "CONDITION");*/

        User user = User.builder().tenantId(tenantId).userId(userId).build();

        ActionContext actionContext = new ActionContext(RequestContext.builder().tenantId(tenantId).user(user).build(), ApiNames.REBATE_OBJ, "Add");
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(ObjectDataDocument.of(rebate));
        try {
            BaseObjectSaveAction.Result result = serviceFacade.triggerRemoteAction(actionContext, arg, BaseObjectSaveAction.Result.class);
            return result.getObjectData().toObjectData();
        } catch (Exception e) {
            log.info("arg:{}", arg, e);
            throw e;
        }
    }

    private boolean hasRebate(String tenantId, IObjectData store, IObjectData agreement) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(RebateFields.ACCOUNT_ID, Operator.EQ, Lists.newArrayList(store.getId())),
                SearchQueryUtil.filter("field_b8zKv__c", Operator.EQ, Lists.newArrayList(agreement.getId()))
        ));
        List<IObjectData> dataList = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.REBATE_OBJ, query, Lists.newArrayList(CommonFields.ID));
        if (CollectionUtils.isEmpty(dataList)) {
            return false;
        } else {
            agreement.set("agreement_cashing_record__c", dataList.get(0).getId());
            return true;
        }
    }

    private boolean hasSentRedPacket(String tenantId, IObjectData store, IObjectData agreement) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(RedPacketRecordObjFields.ACCOUNT_ID, Operator.EQ, Lists.newArrayList(store.getId())),
                SearchQueryUtil.filter(RedPacketRecordObjFields.RELATED_OBJECT_API_NAME, Operator.EQ, Lists.newArrayList(agreement.getDescribeApiName())),
                SearchQueryUtil.filter(RedPacketRecordObjFields.RELATED_OBJECT_DATA_ID, Operator.EQ, Lists.newArrayList(agreement.getId())),
                SearchQueryUtil.filter(RedPacketRecordObjFields.TRIGGER_EVENT, Operator.EQ, Lists.newArrayList(RedPacketRecordObjFields.TriggerEvent.SEND_COUPONS))
        ));
        List<IObjectData> dataList = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.RED_PACKET_RECORD_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.NAME));
        if (CollectionUtils.isEmpty(dataList)) {
            return false;
        } else {
            agreement.set("agreement_red_packet_id__c", dataList.get(0).getId());
            agreement.set("agreement_red_packet_name__c", dataList.get(0).getName());
            return true;
        }
    }

    private <T> T getAgreementMapByFeeType(String tenantId, List<String> agreementIds, List<String> feeTypes, boolean onlyCount) {
        SearchTemplateQuery queryAgreementQuery = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(CommonFields.ID, Operator.IN, agreementIds),
                SearchQueryUtil.filter("field_K6Y5h__c.field_vEf7l__c", Operator.IN, feeTypes, true, null)
        ));
        queryAgreementQuery.setSearchSource("db");

        if (onlyCount) {
            queryAgreementQuery.setNeedReturnCountNum(true);
            queryAgreementQuery.setLimit(1);
            return (T) serviceFacade.countObjectDataFromDB(tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, queryAgreementQuery);
        } else {
            List<IObjectData> agreements = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, queryAgreementQuery, Lists.newArrayList(AGREEMENT_OBJ_DISPLAY_AGREEMENT_ID, CommonFields.NAME, CommonFields.ID, CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, TPMActivityAgreementFields.BEGIN_DATE, TPMActivityAgreementFields.END_DATE, TPMActivityAgreementFields.ACTUAL_TOTAL_AMOUNT));
            log.info("agreements :{}", agreements);

            return (T) agreements.stream().collect(Collectors.toMap(DBRecord::getId, agreement -> agreement, (k1, k2) -> k1));
        }

    }

    private void enterAccount(String tenantId, IObjectData rebateObj, String accountId) {
        EnterAccount.Arg arg = new EnterAccount.Arg();
        EnterAccount.FundAccountInfo fundAccountInfo = new EnterAccount.FundAccountInfo();
        fundAccountInfo.setFundAccountId(accountId);
        arg.setArgs(fundAccountInfo);
        arg.setDataId(rebateObj.getId());
        EnterAccount.Result result = fundAccountProxy.enterAccount(Integer.valueOf(tenantId), -10000, rebateObj.getDescribeApiName(), arg);
        if (result.getCode() != 0) {
            log.info("enter account fail:{}", result);
        }
    }

    private void cancelAccount(String tenantId, String apiName, String dataId) {
        CancelEntry.Arg arg = new CancelEntry.Arg();
        arg.setDataId(dataId);
        CancelEntry.Result result = fundAccountProxy.cancelEntry(Integer.valueOf(tenantId), -10000, apiName, arg);
        if (result.getCode() != 0) {
            log.info("cancel account fail:{},  dataId:{}", result, dataId);
            throw new ValidateException("cancel account fail:" + result.getMessage() + " dataId:" + dataId + "tenantId:" + tenantId);
        }
    }

    private List<IObjectData> findByIdsAndReturnFields(String tenantId, String apiName, List<String> ids, List<String> fields) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(CommonFields.ID, Operator.IN, ids)
        ));
        return QueryDataUtil.find(serviceFacade, tenantId, apiName, query, fields);
    }


    private Map<String, IObjectData> getDealerMap(String tenantId, List<String> tenantAccounts) {
        Map<String, IObjectData> tenantId2StoreMap = new HashMap<>();
        SearchTemplateQuery enterpriseRelationQuery = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(EnterpriseRelationFields.RELATION_TYPE, Operator.EQ, Lists.newArrayList("1"))
        ));

        if (CollectionUtils.isNotEmpty(tenantAccounts)) {
            enterpriseRelationQuery.getFilters().add(SearchQueryUtil.filter(EnterpriseRelationFields.ENTERPRISE_ACCOUNT, Operator.IN, tenantAccounts));
        } else {
            enterpriseRelationQuery.getFilters().add(SearchQueryUtil.filter(EnterpriseRelationFields.ENTERPRISE_ACCOUNT, Operator.ISN, Lists.newArrayList()));
        }

        CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), "EnterpriseRelationObj", enterpriseRelationQuery, (dataList) -> {
            Map<String, String> storeId2TenantIdMap = new HashMap<>();
            dataList.forEach(data -> storeId2TenantIdMap.put(data.get("mapper_account_id", String.class), String.valueOf(eieaConverter.enterpriseAccountToId(data.get("enterprise_account", String.class)))));
            List<IObjectData> stores = serviceFacade.findObjectDataByIdsIncludeDeleted(User.systemUser(tenantId), new ArrayList<>(storeId2TenantIdMap.keySet()), ApiNames.ACCOUNT_OBJ);
            stores.forEach(store -> tenantId2StoreMap.put(storeId2TenantIdMap.get(store.getId()), store));

        });
        return tenantId2StoreMap;
    }

    private List<String> getForbidStoreIds(String tenantId, String dealerStoreId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(SearchQueryUtil.filter("field_A3N37__c", Operator.EQ, Lists.newArrayList(dealerStoreId)), SearchQueryUtil.filter("field_vEw26__c", Operator.EQ, Lists.newArrayList("option1"))));

        return QueryDataUtil.find(serviceFacade, tenantId, "object_12e20__c", query, Lists.newArrayList(CommonFields.ID, "field_O16Yo__c"))
                .stream()
                .map(v -> v.get("field_O16Yo__c", String.class))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private void createDealFlagField(String tenantId) {
        try {
            IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_STORE_WRITE_OFF_OBJ);
            if (describe.getFieldDescribe("send_status__c") == null) {
                serviceFacade.addDescribeCustomField(User.systemUser(tenantId),
                        ApiNames.TPM_STORE_WRITE_OFF_OBJ,
                        "{\"type\":\"select_one\",\"define_type\":\"custom\",\"api_name\":\"send_status__c\",\"label\":\"优惠券发放状态\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"0\",\"default_is_expression\":false,\"default_to_zero\":false,\"options\":[{\"label\":\"已发放\",\"value\":\"1\"},{\"label\":\"未发放\",\"value\":\"0\"},{\"label\":\"其他\",\"value\":\"other\",\"not_usable\":true}],\"is_extend\":true}",
                        Lists.newArrayList(),
                        Lists.newArrayList());
            }
        } catch (Exception e) {
            log.info("createDealFlagField error", e);
        }
    }

    private void createStoreWriteOffField(String tenantId) {
        createNormalField(tenantId, ApiNames.REBATE_OBJ, "store_write_off__c", "{\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\"store_write_off__c\",\"label\":\"门店费用核销ID\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"is_show_mask\":false,\"remove_mask_roles\":{},\"is_extend\":true}");
    }

    private void createScannerField(String tenantId) {
        createNormalField(tenantId, ApiNames.REBATE_OBJ, "scanner__c", "{\"type\":\"employee\",\"define_type\":\"custom\",\"api_name\":\"scanner__c\",\"label\":\"扫码人\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"is_single\":true,\"department_list\":[],\"wheres\":[],\"where_type\":\"field\",\"default_value\":\"\",\"default_is_expression\":false,\"is_extend\":true}");
    }

    private void createOperateField(String tenantId) {
        createNormalField(tenantId, ApiNames.REBATE_OBJ, "operate_message__c", "{\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\"operate_message__c\",\"label\":\"操作记录\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"is_show_mask\":false,\"remove_mask_roles\":{},\"is_extend\":true}");
    }

    private void createRedPacketIdField(String tenantId) {
        createNormalField(tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, "agreement_red_packet_id__c", "{\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\"agreement_red_packet_id__c\",\"label\":\"红包ID\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"is_show_mask\":false,\"remove_mask_roles\":{},\"enable_clone\":true,\"is_extend\":true}");
        createNormalField(tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, "agreement_red_packet_name__c", "{\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\"agreement_red_packet_name__c\",\"label\":\"红包编码\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"is_show_mask\":false,\"remove_mask_roles\":{},\"enable_clone\":true,\"is_extend\":true}");
    }

    private void createCashIdField(String tenantId) {
        createNormalField(tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, "agreement_distribute_obj_id__c", "{\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\"agreement_distribute_obj_id__c\",\"label\":\"优惠券发放单据id\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"is_show_mask\":false,\"remove_mask_roles\":{},\"enable_clone\":true,\"is_extend\":true}");
        createNormalField(tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, "agreement_distribute_obj_name__c", "{\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\"agreement_distribute_obj_name__c\",\"label\":\"优惠券发放单据名称\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"is_show_mask\":false,\"remove_mask_roles\":{},\"enable_clone\":true,\"is_extend\":true}");
        createNormalField(tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, "agreement_distribute_obj_api_name__c", "{\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\"agreement_distribute_obj_api_name__c\",\"label\":\"优惠券发放单据apiName\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"is_show_mask\":false,\"remove_mask_roles\":{},\"enable_clone\":true,\"is_extend\":true}");
        createNormalField(tenantId, UNION_PAY_DETAIL, "cost_month__c", "{\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\"cost_month__c\",\"label\":\"费用月份\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"is_show_mask\":false,\"remove_mask_roles\":{},\"enable_clone\":true,\"is_extend\":true}");
    }

    private void createCostMonthField(String tenantId) {
        createNormalField(tenantId, ApiNames.RED_PACKET_RECORD_OBJ, "cost_month__c", "{\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\"cost_month__c\",\"label\":\"费用月份\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"is_show_mask\":false,\"remove_mask_roles\":{},\"enable_clone\":true,\"is_extend\":true}");
    }


    private void createNormalField(String tenantId, String describeApiName, String fieldName, String fieldDescribe) {
        try {
            IObjectDescribe describe = serviceFacade.findObject(tenantId, describeApiName);
            if (describe.getFieldDescribe(fieldName) == null) {
                serviceFacade.addDescribeCustomField(User.systemUser(tenantId),
                        describeApiName,
                        fieldDescribe,
                        Lists.newArrayList(),
                        Lists.newArrayList());
            }
        } catch (Exception e) {
            log.info("createNormalField error", e);
        }
    }

    private void validateRewardTypeAndPayAccount(String rewardType, String payAccountType, IObjectData dealer) {
        if (rewardType.equals("redPacket")) {
            if (Strings.isNullOrEmpty(payAccountType)) {
                throw new ValidateException(I18N.text(I18NKeys.VALIDATE_DISTRIBUTION_SERVICE_0));
            } else if ("masterCloud".equals(payAccountType) || "dealerCloud".equals(payAccountType)) {
                throw new ValidateException("红包对应的发放账户未支持，请更换");
            }
        }
        if (rewardType.equals("cash")) {
            if (Strings.isNullOrEmpty(payAccountType) || !payAccountType.equals("marketUnionPayAccount")) {
                throw new ValidateException("请选择合适的现金发放账户。");
            }
            String accountFiled = dealer.get("unionpay_withdrawal_accoun__c", String.class);
            if (Strings.isNullOrEmpty(accountFiled)) {
                throw new ValidateException("经销商银联账户未设置。dealerId:" + dealer.getId());
            }
        }
    }
}
